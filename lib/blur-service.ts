// Blur service using custom blur API
import { BASE_URL } from "@/components/ImageEditor";

export interface BlurOptions {
  type: 'gaussian' | 'motion';
  radius: number;
  angle?: number; // 仅用于运动模糊
}

export interface BlurResult {
  success: boolean;
  result?: string;
  type?: string;
  radius?: number;
  angle?: number;
  original_size?: [number, number];
  result_size?: [number, number];
  error?: string;
}

// Convert canvas to base64
export const canvasToBase64 = (canvas: HTMLCanvasElement): string => {
  return canvas.toDataURL('image/png');
};

// Convert image URL to base64
export const imageUrlToBase64 = (url: string): Promise<string> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = 'anonymous';
    img.onload = () => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      if (!ctx) {
        reject(new Error('Failed to get canvas context'));
        return;
      }
      
      canvas.width = img.width;
      canvas.height = img.height;
      ctx.drawImage(img, 0, 0);
      
      const dataURL = canvas.toDataURL('image/png');
      const base64 = dataURL.split(',')[1];
      resolve(base64);
    };
    img.onerror = reject;
    img.src = url;
  });
};

// Apply blur effect using custom blur API
export const applyBlur = async (
  imageBase64: string,
  options: BlurOptions
): Promise<string> => {
  const { type = 'gaussian', radius = 5, angle = 0 } = options;

  try {
    // Prepare request body
    const requestBody = {
      image: imageBase64,
      type: type,
      radius: radius,
      angle: type === 'motion' ? angle : undefined
    };

    // Call blur API (use port 7862 for blur service)
    const blurApiUrl = BASE_URL.replace(':7860', ':7862');
    const response = await fetch(`${blurApiUrl}/api/v1/blur`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Blur API error: ${response.status} ${response.statusText} - ${errorText}`);
    }

    const result: BlurResult = await response.json();

    if (!result.success) {
      throw new Error(result.error || 'Blur processing failed');
    }

    if (!result.result) {
      throw new Error('No result returned from blur API');
    }

    // Return base64 result
    return result.result;

  } catch (error) {
    console.error('Blur error:', error);
    throw error;
  }
};

// Check blur service health
export const checkBlurHealth = async (): Promise<boolean> => {
  try {
    const blurApiUrl = BASE_URL.replace(':7860', ':7862');
    const response = await fetch(`${blurApiUrl}/api/v1/blur/health`);
    
    if (response.ok) {
      const health = await response.json();
      return health.status === 'healthy';
    }
    
    return false;
  } catch (error) {
    console.error('Blur health check failed:', error);
    return false;
  }
};

// Validate blur parameters
export const validateBlurParams = (options: BlurOptions): { valid: boolean; error?: string } => {
  const { type, radius, angle } = options;

  if (!['gaussian', 'motion'].includes(type)) {
    return { valid: false, error: 'Invalid blur type. Must be "gaussian" or "motion"' };
  }

  if (!Number.isInteger(radius) || radius < 1 || radius > 50) {
    return { valid: false, error: 'Blur radius must be an integer between 1 and 50' };
  }

  if (type === 'motion') {
    if (angle === undefined || !Number.isInteger(angle) || angle < 0 || angle > 360) {
      return { valid: false, error: 'Motion blur angle must be an integer between 0 and 360' };
    }
  }

  return { valid: true };
};

// Apply blur with automatic image conversion
export const applyBlurToImage = async (
  imageUrl: string,
  options: BlurOptions
): Promise<string> => {
  try {
    // Validate parameters
    const validation = validateBlurParams(options);
    if (!validation.valid) {
      throw new Error(validation.error);
    }

    // Convert image to base64
    const imageBase64 = await imageUrlToBase64(imageUrl);
    
    // Apply blur
    const resultBase64 = await applyBlur(imageBase64, options);
    
    // Return as data URL
    return `data:image/png;base64,${resultBase64}`;
    
  } catch (error) {
    console.error('Apply blur to image error:', error);
    throw error;
  }
};

// Preset blur configurations
export const BLUR_PRESETS = {
  light_gaussian: { type: 'gaussian' as const, radius: 3 },
  medium_gaussian: { type: 'gaussian' as const, radius: 8 },
  heavy_gaussian: { type: 'gaussian' as const, radius: 15 },
  horizontal_motion: { type: 'motion' as const, radius: 10, angle: 0 },
  vertical_motion: { type: 'motion' as const, radius: 10, angle: 90 },
  diagonal_motion: { type: 'motion' as const, radius: 10, angle: 45 },
};

// Get blur preset by name
export const getBlurPreset = (presetName: keyof typeof BLUR_PRESETS): BlurOptions => {
  return BLUR_PRESETS[presetName];
};
