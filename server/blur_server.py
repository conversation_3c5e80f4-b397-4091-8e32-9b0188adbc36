#!/usr/bin/env python3
"""
Simple HTTP server for blur functionality
Runs alongside IOPaint on the same port using a different path
"""

import http.server
import socketserver
import json
import urllib.parse
import threading
import time
import logging
from blur_plugin_iopaint import process_blur_request

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class BlurRequestHandler(http.server.BaseHTTPRequestHandler):
    """HTTP request handler for blur API"""
    
    def do_POST(self):
        """Handle POST requests"""
        try:
            # Parse URL
            parsed_path = urllib.parse.urlparse(self.path)
            
            if parsed_path.path == '/api/v1/blur':
                self.handle_blur_request()
            else:
                self.send_error(404, "Not Found")
                
        except Exception as e:
            logger.error(f"Error handling POST request: {e}")
            self.send_error(500, "Internal Server Error")
    
    def do_GET(self):
        """Handle GET requests"""
        try:
            parsed_path = urllib.parse.urlparse(self.path)
            
            if parsed_path.path == '/api/v1/blur/health':
                self.handle_health_check()
            else:
                self.send_error(404, "Not Found")
                
        except Exception as e:
            logger.error(f"Error handling GET request: {e}")
            self.send_error(500, "Internal Server Error")
    
    def handle_blur_request(self):
        """Handle blur processing request"""
        try:
            # Read request body
            content_length = int(self.headers.get('Content-Length', 0))
            if content_length == 0:
                self.send_json_response({'error': 'No request body'}, 400)
                return
            
            body = self.rfile.read(content_length)
            data = json.loads(body.decode('utf-8'))
            
            # Extract parameters
            image_base64 = data.get('image')
            blur_type = data.get('type', 'gaussian')
            blur_radius = data.get('radius', 5)
            angle = data.get('angle', 0)
            
            if not image_base64:
                self.send_json_response({'error': 'No image data provided'}, 400)
                return
            
            # Validate parameters
            try:
                blur_radius = int(blur_radius)
                angle = int(angle) if angle is not None else 0
            except (ValueError, TypeError):
                self.send_json_response({'error': 'Invalid parameter types'}, 400)
                return
            
            # Process blur request
            result = process_blur_request(image_base64, blur_type, blur_radius, angle)
            
            if result['success']:
                self.send_json_response(result, 200)
            else:
                self.send_json_response({'error': result['error']}, 400)
                
        except json.JSONDecodeError:
            self.send_json_response({'error': 'Invalid JSON'}, 400)
        except Exception as e:
            logger.error(f"Blur request error: {e}")
            self.send_json_response({'error': 'Internal server error'}, 500)
    
    def handle_health_check(self):
        """Handle health check request"""
        response = {
            'status': 'healthy',
            'service': 'blur-service',
            'max_blur_radius': 50,
            'max_image_size': 2048
        }
        self.send_json_response(response, 200)
    
    def send_json_response(self, data, status_code):
        """Send JSON response"""
        response_body = json.dumps(data).encode('utf-8')
        
        self.send_response(status_code)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Content-Length', str(len(response_body)))
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
        
        self.wfile.write(response_body)
    
    def do_OPTIONS(self):
        """Handle CORS preflight requests"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
    
    def log_message(self, format, *args):
        """Override to use our logger"""
        logger.info(f"{self.address_string()} - {format % args}")

def start_blur_server(port=7862):
    """Start the blur HTTP server"""
    try:
        with socketserver.TCPServer(("", port), BlurRequestHandler) as httpd:
            logger.info(f"🎨 Blur server started on port {port}")
            httpd.serve_forever()
    except Exception as e:
        logger.error(f"❌ Error starting blur server: {e}")

def start_blur_server_thread(port=7862):
    """Start blur server in a background thread"""
    thread = threading.Thread(target=start_blur_server, args=(port,), daemon=True)
    thread.start()
    return thread

if __name__ == "__main__":
    # Test the server
    print("Starting blur server on port 7862...")
    start_blur_server(7862)
