#!/usr/bin/env python3
"""
Test script for blur service
"""

import requests
import base64
import json
import sys
from PIL import Image
import io

def create_test_image():
    """创建一个测试图片"""
    # 创建一个简单的测试图片
    img = Image.new('RGB', (200, 200), color='red')
    
    # 添加一些图案
    from PIL import ImageDraw
    draw = ImageDraw.Draw(img)
    draw.rectangle([50, 50, 150, 150], fill='blue')
    draw.ellipse([75, 75, 125, 125], fill='yellow')
    
    # 转换为base64
    buffer = io.BytesIO()
    img.save(buffer, format='PNG')
    return base64.b64encode(buffer.getvalue()).decode('utf-8')

def test_blur_api(base_url="http://localhost:7861"):
    """测试模糊API"""
    print("🧪 Testing Blur API...")
    
    # 创建测试图片
    test_image_b64 = create_test_image()
    print("✅ Test image created")
    
    # 测试健康检查
    try:
        response = requests.get(f"{base_url}/api/v1/blur/health")
        if response.status_code == 200:
            health_data = response.json()
            print(f"✅ Health check passed: {health_data}")
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False
    
    # 测试高斯模糊
    print("\n🔄 Testing Gaussian Blur...")
    try:
        payload = {
            "image": f"data:image/png;base64,{test_image_b64}",
            "type": "gaussian",
            "radius": 5
        }
        
        response = requests.post(
            f"{base_url}/api/v1/blur",
            json=payload,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ Gaussian blur successful: {result['type']}, radius: {result['radius']}")
                
                # 保存结果图片（可选）
                if 'result' in result:
                    result_image_data = base64.b64decode(result['result'])
                    with open('test_gaussian_blur.png', 'wb') as f:
                        f.write(result_image_data)
                    print("💾 Result saved as test_gaussian_blur.png")
            else:
                print(f"❌ Gaussian blur failed: {result.get('error')}")
                return False
        else:
            print(f"❌ Gaussian blur request failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Gaussian blur error: {e}")
        return False
    
    # 测试运动模糊
    print("\n🔄 Testing Motion Blur...")
    try:
        payload = {
            "image": f"data:image/png;base64,{test_image_b64}",
            "type": "motion",
            "radius": 8,
            "angle": 45
        }
        
        response = requests.post(
            f"{base_url}/api/v1/blur",
            json=payload,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ Motion blur successful: {result['type']}, radius: {result['radius']}, angle: {result['angle']}")
                
                # 保存结果图片（可选）
                if 'result' in result:
                    result_image_data = base64.b64decode(result['result'])
                    with open('test_motion_blur.png', 'wb') as f:
                        f.write(result_image_data)
                    print("💾 Result saved as test_motion_blur.png")
            else:
                print(f"❌ Motion blur failed: {result.get('error')}")
                return False
        else:
            print(f"❌ Motion blur request failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Motion blur error: {e}")
        return False
    
    # 测试错误处理
    print("\n🔄 Testing Error Handling...")
    try:
        # 测试无效的模糊类型
        payload = {
            "image": f"data:image/png;base64,{test_image_b64}",
            "type": "invalid_type",
            "radius": 5
        }
        
        response = requests.post(
            f"{base_url}/api/v1/blur",
            json=payload,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 400:
            print("✅ Invalid blur type correctly rejected")
        else:
            print(f"❌ Expected 400 error for invalid type, got {response.status_code}")
        
        # 测试无效的半径
        payload = {
            "image": f"data:image/png;base64,{test_image_b64}",
            "type": "gaussian",
            "radius": 100  # 超过最大值
        }
        
        response = requests.post(
            f"{base_url}/api/v1/blur",
            json=payload,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 400:
            print("✅ Invalid radius correctly rejected")
        else:
            print(f"❌ Expected 400 error for invalid radius, got {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error handling test error: {e}")
        return False
    
    print("\n🎉 All tests passed!")
    return True

def main():
    """主函数"""
    if len(sys.argv) > 1:
        base_url = sys.argv[1]
    else:
        base_url = "http://localhost:7861"
    
    print(f"Testing blur service at: {base_url}")
    
    success = test_blur_api(base_url)
    
    if success:
        print("\n✅ Blur service is working correctly!")
        sys.exit(0)
    else:
        print("\n❌ Blur service tests failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
