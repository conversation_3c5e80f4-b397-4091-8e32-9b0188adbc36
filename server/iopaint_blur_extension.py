#!/usr/bin/env python3
"""
IOPaint Blur Extension
Monkey patches IOPaint to add blur functionality
"""

import cv2
import numpy as np
from PIL import Image
import io
import base64
import json
import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)

class BlurProcessor:
    """Blur processing functionality"""
    
    def __init__(self):
        self.max_image_size = 2048
        self.max_blur_radius = 50
    
    def process_blur_request(self, image_base64: str, blur_type: str = 'gaussian', 
                           radius: int = 5, angle: int = 0) -> Dict[str, Any]:
        """Process blur request with base64 image"""
        try:
            # Decode base64 image
            if ',' in image_base64:
                image_base64 = image_base64.split(',')[1]
            
            image_data = base64.b64decode(image_base64)
            pil_image = Image.open(io.BytesIO(image_data))
            
            # Resize if too large
            if pil_image.width > self.max_image_size or pil_image.height > self.max_image_size:
                ratio = min(self.max_image_size / pil_image.width, 
                           self.max_image_size / pil_image.height)
                new_size = (int(pil_image.width * ratio), int(pil_image.height * ratio))
                pil_image = pil_image.resize(new_size, Image.Resampling.LANCZOS)
            
            # Convert to RGB if needed
            if pil_image.mode != 'RGB':
                pil_image = pil_image.convert('RGB')
            
            # Convert to numpy array
            image_array = np.array(pil_image)
            
            # Apply blur
            if blur_type == 'gaussian':
                blurred_array = self.apply_gaussian_blur(image_array, radius)
            elif blur_type == 'motion':
                blurred_array = self.apply_motion_blur(image_array, radius, angle)
            else:
                raise ValueError(f"Unsupported blur type: {blur_type}")
            
            # Convert back to PIL and base64
            result_image = Image.fromarray(blurred_array.astype(np.uint8))
            buffer = io.BytesIO()
            result_image.save(buffer, format='PNG', optimize=True)
            result_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
            
            return {
                'success': True,
                'result': result_base64,
                'type': blur_type,
                'radius': radius,
                'angle': angle if blur_type == 'motion' else None,
                'original_size': [pil_image.width, pil_image.height],
                'result_size': [result_image.width, result_image.height]
            }
            
        except Exception as e:
            logger.error(f"Blur processing failed: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def apply_gaussian_blur(self, image: np.ndarray, radius: int) -> np.ndarray:
        """Apply Gaussian blur"""
        try:
            if radius < 1 or radius > self.max_blur_radius:
                raise ValueError(f"Blur radius must be between 1 and {self.max_blur_radius}")
            
            kernel_size = radius * 2 + 1
            bgr_image = cv2.cvtColor(image, cv2.COLOR_RGB2BGR)
            blurred_bgr = cv2.GaussianBlur(bgr_image, (kernel_size, kernel_size), 0)
            blurred_rgb = cv2.cvtColor(blurred_bgr, cv2.COLOR_BGR2RGB)
            
            return blurred_rgb
        except Exception as e:
            logger.error(f"Gaussian blur failed: {e}")
            return image
    
    def apply_motion_blur(self, image: np.ndarray, size: int, angle: int) -> np.ndarray:
        """Apply motion blur"""
        try:
            if size < 3 or size > self.max_blur_radius * 2:
                raise ValueError(f"Motion blur size must be between 3 and {self.max_blur_radius * 2}")
            
            if angle < 0 or angle > 360:
                raise ValueError("Angle must be between 0 and 360 degrees")
            
            kernel = self._create_motion_blur_kernel(size, angle)
            bgr_image = cv2.cvtColor(image, cv2.COLOR_RGB2BGR)
            blurred_bgr = cv2.filter2D(bgr_image, -1, kernel)
            blurred_rgb = cv2.cvtColor(blurred_bgr, cv2.COLOR_BGR2RGB)
            
            return blurred_rgb
        except Exception as e:
            logger.error(f"Motion blur failed: {e}")
            return image
    
    def _create_motion_blur_kernel(self, size: int, angle: int) -> np.ndarray:
        """Create motion blur kernel"""
        if size % 2 == 0:
            size += 1
        
        kernel = np.zeros((size, size), dtype=np.float32)
        kernel[int((size-1)/2), :] = np.ones(size, dtype=np.float32)
        kernel = kernel / size
        
        if angle != 0:
            center = (size // 2, size // 2)
            M = cv2.getRotationMatrix2D(center, angle, 1.0)
            kernel = cv2.warpAffine(kernel, M, (size, size))
        
        return kernel

# Global blur processor instance
blur_processor = BlurProcessor()

def patch_iopaint_app():
    """Monkey patch IOPaint app to add blur endpoints"""
    try:
        # This will be called after IOPaint starts
        import iopaint
        from fastapi import Request
        from fastapi.responses import JSONResponse
        
        # Get the FastAPI app instance
        app = iopaint.app
        
        @app.post("/api/v1/blur")
        async def blur_endpoint(request: Request):
            """Blur endpoint"""
            try:
                data = await request.json()
                
                image_base64 = data.get('image')
                blur_type = data.get('type', 'gaussian')
                blur_radius = int(data.get('radius', 5))
                angle = int(data.get('angle', 0))
                
                if not image_base64:
                    return JSONResponse(
                        status_code=400,
                        content={'error': 'No image data provided'}
                    )
                
                result = blur_processor.process_blur_request(
                    image_base64, blur_type, blur_radius, angle
                )
                
                if result['success']:
                    return JSONResponse(content=result)
                else:
                    return JSONResponse(
                        status_code=400,
                        content={'error': result['error']}
                    )
                    
            except Exception as e:
                logger.error(f"Blur endpoint error: {e}")
                return JSONResponse(
                    status_code=500,
                    content={'error': 'Internal server error'}
                )
        
        @app.get("/api/v1/blur/health")
        async def blur_health_endpoint():
            """Blur health check endpoint"""
            return JSONResponse(content={
                'status': 'healthy',
                'service': 'blur-service',
                'max_blur_radius': blur_processor.max_blur_radius,
                'max_image_size': blur_processor.max_image_size
            })
        
        logger.info("✅ Blur endpoints added to IOPaint app")
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to patch IOPaint app: {e}")
        return False

def setup_blur_extension():
    """Setup blur extension"""
    try:
        # Import required modules
        import time
        import threading
        
        def delayed_patch():
            """Patch IOPaint after it starts"""
            time.sleep(5)  # Wait for IOPaint to fully start
            patch_iopaint_app()
        
        # Start patching in background
        patch_thread = threading.Thread(target=delayed_patch, daemon=True)
        patch_thread.start()
        
        logger.info("🎨 Blur extension setup initiated")
        
    except Exception as e:
        logger.error(f"❌ Blur extension setup failed: {e}")

if __name__ == "__main__":
    # Test the blur processor
    print("Testing BlurProcessor...")
    
    # Create a test image
    test_image = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
    test_pil = Image.fromarray(test_image)
    
    # Convert to base64
    buffer = io.BytesIO()
    test_pil.save(buffer, format='PNG')
    test_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
    
    # Test Gaussian blur
    result = blur_processor.process_blur_request(test_base64, 'gaussian', 5)
    print(f"Gaussian blur test: {'✅' if result['success'] else '❌'}")
    
    # Test Motion blur
    result = blur_processor.process_blur_request(test_base64, 'motion', 10, 45)
    print(f"Motion blur test: {'✅' if result['success'] else '❌'}")
    
    print("BlurProcessor test completed!")
