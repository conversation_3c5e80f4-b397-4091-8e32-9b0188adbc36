#!/usr/bin/env python3
"""
IOPaint Blur Plugin
Integrates blur functionality as an IOPaint plugin
"""

import cv2
import numpy as np
from PIL import Image
import io
import base64
import logging
from typing import Optional, Dict, Any

logger = logging.getLogger(__name__)

class BlurPlugin:
    """IOPaint compatible blur plugin"""
    
    def __init__(self):
        self.name = "BlurPlugin"
        self.description = "Apply Gaussian and Motion blur effects"
        self.max_image_size = 2048
        self.max_blur_radius = 50
        logger.info("BlurPlugin initialized")
    
    def __call__(self, image: np.ndarray, **kwargs) -> np.ndarray:
        """
        Main plugin entry point for IOPaint
        
        Args:
            image: Input image as numpy array (H, W, C) in RGB format
            **kwargs: Plugin parameters
                - blur_type: 'gaussian' or 'motion'
                - radius: blur radius (1-50)
                - angle: motion blur angle (0-360, only for motion blur)
        
        Returns:
            numpy.ndarray: Blurred image
        """
        try:
            blur_type = kwargs.get('blur_type', 'gaussian')
            radius = int(kwargs.get('radius', 5))
            angle = int(kwargs.get('angle', 0))
            
            logger.info(f"Applying {blur_type} blur with radius {radius}")
            
            if blur_type == 'gaussian':
                return self.apply_gaussian_blur(image, radius)
            elif blur_type == 'motion':
                return self.apply_motion_blur(image, radius, angle)
            else:
                raise ValueError(f"Unsupported blur type: {blur_type}")
                
        except Exception as e:
            logger.error(f"Blur plugin error: {e}")
            return image  # Return original image on error
    
    def apply_gaussian_blur(self, image: np.ndarray, radius: int) -> np.ndarray:
        """Apply Gaussian blur to image"""
        try:
            # Validate radius
            if radius < 1 or radius > self.max_blur_radius:
                raise ValueError(f"Blur radius must be between 1 and {self.max_blur_radius}")
            
            # Ensure kernel size is odd
            kernel_size = radius * 2 + 1
            
            # Convert RGB to BGR for OpenCV
            bgr_image = cv2.cvtColor(image, cv2.COLOR_RGB2BGR)
            
            # Apply Gaussian blur
            blurred_bgr = cv2.GaussianBlur(bgr_image, (kernel_size, kernel_size), 0)
            
            # Convert back to RGB
            blurred_rgb = cv2.cvtColor(blurred_bgr, cv2.COLOR_BGR2RGB)
            
            return blurred_rgb
            
        except Exception as e:
            logger.error(f"Gaussian blur failed: {e}")
            return image
    
    def apply_motion_blur(self, image: np.ndarray, size: int, angle: int) -> np.ndarray:
        """Apply motion blur to image"""
        try:
            # Validate parameters
            if size < 3 or size > self.max_blur_radius * 2:
                raise ValueError(f"Motion blur size must be between 3 and {self.max_blur_radius * 2}")
            
            if angle < 0 or angle > 360:
                raise ValueError("Angle must be between 0 and 360 degrees")
            
            # Create motion blur kernel
            kernel = self._create_motion_blur_kernel(size, angle)
            
            # Convert RGB to BGR for OpenCV
            bgr_image = cv2.cvtColor(image, cv2.COLOR_RGB2BGR)
            
            # Apply motion blur
            blurred_bgr = cv2.filter2D(bgr_image, -1, kernel)
            
            # Convert back to RGB
            blurred_rgb = cv2.cvtColor(blurred_bgr, cv2.COLOR_BGR2RGB)
            
            return blurred_rgb
            
        except Exception as e:
            logger.error(f"Motion blur failed: {e}")
            return image
    
    def _create_motion_blur_kernel(self, size: int, angle: int) -> np.ndarray:
        """Create motion blur kernel"""
        # Ensure size is odd
        if size % 2 == 0:
            size += 1
        
        # Create horizontal line kernel
        kernel = np.zeros((size, size), dtype=np.float32)
        kernel[int((size-1)/2), :] = np.ones(size, dtype=np.float32)
        kernel = kernel / size
        
        # Rotate kernel if angle is not 0
        if angle != 0:
            center = (size // 2, size // 2)
            M = cv2.getRotationMatrix2D(center, angle, 1.0)
            kernel = cv2.warpAffine(kernel, M, (size, size))
        
        return kernel

# Create global plugin instance
blur_plugin = BlurPlugin()

def process_blur_request(image_base64: str, blur_type: str = 'gaussian', 
                        radius: int = 5, angle: int = 0) -> Dict[str, Any]:
    """
    Process blur request with base64 image
    
    Args:
        image_base64: Base64 encoded image
        blur_type: 'gaussian' or 'motion'
        radius: Blur radius
        angle: Motion blur angle
    
    Returns:
        Dict with success status and result
    """
    try:
        # Decode base64 image
        if ',' in image_base64:
            image_base64 = image_base64.split(',')[1]
        
        image_data = base64.b64decode(image_base64)
        pil_image = Image.open(io.BytesIO(image_data))
        
        # Resize if too large
        if pil_image.width > blur_plugin.max_image_size or pil_image.height > blur_plugin.max_image_size:
            ratio = min(blur_plugin.max_image_size / pil_image.width, 
                       blur_plugin.max_image_size / pil_image.height)
            new_size = (int(pil_image.width * ratio), int(pil_image.height * ratio))
            pil_image = pil_image.resize(new_size, Image.Resampling.LANCZOS)
        
        # Convert to RGB if needed
        if pil_image.mode != 'RGB':
            pil_image = pil_image.convert('RGB')
        
        # Convert to numpy array
        image_array = np.array(pil_image)
        
        # Apply blur
        blurred_array = blur_plugin(image_array, blur_type=blur_type, radius=radius, angle=angle)
        
        # Convert back to PIL and base64
        result_image = Image.fromarray(blurred_array.astype(np.uint8))
        buffer = io.BytesIO()
        result_image.save(buffer, format='PNG', optimize=True)
        result_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
        
        return {
            'success': True,
            'result': result_base64,
            'type': blur_type,
            'radius': radius,
            'angle': angle if blur_type == 'motion' else None,
            'original_size': [pil_image.width, pil_image.height],
            'result_size': [result_image.width, result_image.height]
        }
        
    except Exception as e:
        logger.error(f"Blur processing failed: {e}")
        return {
            'success': False,
            'error': str(e)
        }

if __name__ == "__main__":
    # Test the plugin
    print("Testing BlurPlugin...")
    
    # Create a test image
    test_image = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
    
    # Test Gaussian blur
    blurred = blur_plugin(test_image, blur_type='gaussian', radius=5)
    print(f"Gaussian blur test: {blurred.shape}")
    
    # Test Motion blur
    blurred = blur_plugin(test_image, blur_type='motion', radius=10, angle=45)
    print(f"Motion blur test: {blurred.shape}")
    
    print("BlurPlugin test completed!")
