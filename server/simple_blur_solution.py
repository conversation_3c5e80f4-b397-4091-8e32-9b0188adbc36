#!/usr/bin/env python3
"""
Simple file-based blur solution
Uses file system to communicate between frontend and backend
"""

import os
import json
import base64
import time
import threading
import logging
from pathlib import Path
from blur_plugin_iopaint import blur_processor

logger = logging.getLogger(__name__)

class FileBasedBlurService:
    """File-based blur service"""
    
    def __init__(self, work_dir="/tmp/blur_service"):
        self.work_dir = Path(work_dir)
        self.work_dir.mkdir(exist_ok=True)
        self.request_dir = self.work_dir / "requests"
        self.response_dir = self.work_dir / "responses"
        self.request_dir.mkdir(exist_ok=True)
        self.response_dir.mkdir(exist_ok=True)
        self.running = False
        
    def start(self):
        """Start the file-based service"""
        self.running = True
        logger.info("🎨 Starting file-based blur service...")
        
        while self.running:
            try:
                self.process_requests()
                time.sleep(0.5)  # Check every 500ms
            except Exception as e:
                logger.error(f"Error in blur service loop: {e}")
                time.sleep(1)
    
    def process_requests(self):
        """Process pending blur requests"""
        for request_file in self.request_dir.glob("*.json"):
            try:
                # Read request
                with open(request_file, 'r') as f:
                    request_data = json.load(f)
                
                request_id = request_file.stem
                logger.info(f"Processing blur request: {request_id}")
                
                # Process blur
                result = blur_processor.process_blur_request(
                    request_data.get('image', ''),
                    request_data.get('type', 'gaussian'),
                    request_data.get('radius', 5),
                    request_data.get('angle', 0)
                )
                
                # Write response
                response_file = self.response_dir / f"{request_id}.json"
                with open(response_file, 'w') as f:
                    json.dump(result, f)
                
                # Remove request file
                request_file.unlink()
                
                logger.info(f"✅ Completed blur request: {request_id}")
                
            except Exception as e:
                logger.error(f"Error processing request {request_file}: {e}")
                # Remove problematic request
                try:
                    request_file.unlink()
                except:
                    pass
    
    def stop(self):
        """Stop the service"""
        self.running = False

# Global service instance
file_blur_service = FileBasedBlurService()

def start_file_blur_service():
    """Start file-based blur service in background"""
    try:
        service_thread = threading.Thread(target=file_blur_service.start, daemon=True)
        service_thread.start()
        logger.info("✅ File-based blur service started")
    except Exception as e:
        logger.error(f"❌ Failed to start file-based blur service: {e}")

if __name__ == "__main__":
    # Test the service
    print("Testing file-based blur service...")
    
    # Create test request
    test_request = {
        "image": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==",
        "type": "gaussian",
        "radius": 5
    }
    
    service = FileBasedBlurService("/tmp/test_blur")
    
    # Write test request
    request_file = service.request_dir / "test_request.json"
    with open(request_file, 'w') as f:
        json.dump(test_request, f)
    
    # Process once
    service.process_requests()
    
    # Check response
    response_file = service.response_dir / "test_request.json"
    if response_file.exists():
        with open(response_file, 'r') as f:
            result = json.load(f)
        print(f"✅ Test result: {result.get('success', False)}")
    else:
        print("❌ No response generated")
    
    print("File-based blur service test completed!")
