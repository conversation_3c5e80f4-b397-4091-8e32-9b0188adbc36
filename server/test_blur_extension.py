#!/usr/bin/env python3
"""
Test script for IOPaint blur extension
"""

import requests
import base64
import json
import sys
from PIL import Image
import io
import time

def create_test_image():
    """创建一个测试图片"""
    img = Image.new('RGB', (200, 200), color='red')
    
    from PIL import ImageDraw
    draw = ImageDraw.Draw(img)
    draw.rectangle([50, 50, 150, 150], fill='blue')
    draw.ellipse([75, 75, 125, 125], fill='yellow')
    
    buffer = io.BytesIO()
    img.save(buffer, format='PNG')
    return base64.b64encode(buffer.getvalue()).decode('utf-8')

def test_iopaint_blur(base_url="http://localhost:7860"):
    """测试IOPaint模糊扩展"""
    print("🧪 Testing IOPaint Blur Extension...")
    
    # 创建测试图片
    test_image_b64 = create_test_image()
    print("✅ Test image created")
    
    # 等待IOPaint启动
    print("\n⏳ Waiting for IOPaint to start...")
    for i in range(30):
        try:
            response = requests.get(f"{base_url}/api/v1/server-config", timeout=2)
            if response.status_code == 200:
                print(f"✅ IOPaint is ready after {i+1} seconds")
                break
        except:
            pass
        time.sleep(1)
    else:
        print("❌ IOPaint not ready after 30 seconds")
        return False
    
    # 等待模糊扩展加载
    print("\n⏳ Waiting for blur extension to load...")
    time.sleep(10)  # 给扩展更多时间加载
    
    # 测试健康检查
    try:
        print("\n🔄 Testing blur health check...")
        response = requests.get(f"{base_url}/api/v1/blur/health", timeout=10)
        if response.status_code == 200:
            health_data = response.json()
            print(f"✅ Blur health check passed: {health_data}")
        else:
            print(f"❌ Blur health check failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Blur health check error: {e}")
        return False
    
    # 测试高斯模糊
    print("\n🔄 Testing Gaussian Blur...")
    try:
        payload = {
            "image": f"data:image/png;base64,{test_image_b64}",
            "type": "gaussian",
            "radius": 5
        }
        
        response = requests.post(
            f"{base_url}/api/v1/blur",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ Gaussian blur successful: {result['type']}, radius: {result['radius']}")
                
                # 保存结果图片
                if 'result' in result:
                    result_image_data = base64.b64decode(result['result'])
                    with open('test_gaussian_blur_extension.png', 'wb') as f:
                        f.write(result_image_data)
                    print("💾 Result saved as test_gaussian_blur_extension.png")
            else:
                print(f"❌ Gaussian blur failed: {result.get('error')}")
                return False
        else:
            print(f"❌ Gaussian blur request failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Gaussian blur error: {e}")
        return False
    
    # 测试运动模糊
    print("\n🔄 Testing Motion Blur...")
    try:
        payload = {
            "image": f"data:image/png;base64,{test_image_b64}",
            "type": "motion",
            "radius": 8,
            "angle": 45
        }
        
        response = requests.post(
            f"{base_url}/api/v1/blur",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ Motion blur successful: {result['type']}, radius: {result['radius']}, angle: {result['angle']}")
                
                # 保存结果图片
                if 'result' in result:
                    result_image_data = base64.b64decode(result['result'])
                    with open('test_motion_blur_extension.png', 'wb') as f:
                        f.write(result_image_data)
                    print("💾 Result saved as test_motion_blur_extension.png")
            else:
                print(f"❌ Motion blur failed: {result.get('error')}")
                return False
        else:
            print(f"❌ Motion blur request failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Motion blur error: {e}")
        return False
    
    print("\n🎉 All tests passed!")
    return True

def main():
    """主函数"""
    if len(sys.argv) > 1:
        base_url = sys.argv[1]
    else:
        base_url = "http://localhost:7860"
    
    print(f"Testing IOPaint blur extension at: {base_url}")
    
    success = test_iopaint_blur(base_url)
    
    if success:
        print("\n✅ IOPaint blur extension is working correctly!")
        sys.exit(0)
    else:
        print("\n❌ IOPaint blur extension tests failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
