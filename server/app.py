#!/usr/bin/env python3
"""
Simple IOPaint server launcher for Hugging Face Space
Uses IOPaint's built-in CLI to avoid dependency conflicts
"""

import subprocess
import sys
import os

def start_iopaint():
    """启动IOPaint服务"""
    # Set environment variables for better performance
    os.environ["PYTORCH_ENABLE_MPS_FALLBACK"] = "1"
    os.environ["ONEDNN_PRIMITIVE_CACHE_CAPACITY"] = "1024"  # 增加缓存
    os.environ["LRU_CACHE_CAPACITY"] = "1024"
    os.environ["TORCH_CUDNN_V8_API_LRU_CACHE_LIMIT"] = "1024"
    os.environ["OMP_NUM_THREADS"] = "8"  # 利用多核 CPU
    os.environ["MKL_NUM_THREADS"] = "8"
    os.environ["NUMEXPR_NUM_THREADS"] = "8"

    # 检测可用设备
    device = "cpu"
    try:
        import torch
        if torch.cuda.is_available():
            device = "cuda"
            logger.info("🚀 CUDA GPU detected, using GPU acceleration")
        else:
            logger.info("💻 Using CPU mode with optimizations")
    except ImportError:
        logger.info("⚠️  PyTorch not available, using CPU mode")

    # IOPaint CLI command with compatible optimizations
    cmd = [
        "iopaint", "start",
        "--model", "lama",  # 快速且高质量的模型
        "--host", "0.0.0.0",
        "--port", "7860",
        "--device", device,
        "--disable-nsfw-checker",
         "--enable-remove-bg"  # 启用背景移除功能
    ]

    # 根据设备类型添加优化参数
    if device == "cuda":
        # GPU 模式优化
        cmd.extend([
            "--cpu-offload",  # CPU 卸载以节省显存
            "--no-half"       # 避免半精度问题
        ])
    else:
        # CPU 模式优化
        cmd.extend([
            "--low-mem",      # 低内存模式
            "--cpu-textencoder"  # CPU 文本编码器
        ])

    logger.info("🎯 Starting IOPaint server...")
    logger.info(f"Command: {' '.join(cmd)}")

    try:
        # Run IOPaint CLI directly
        subprocess.run(cmd, check=True)
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ Error starting IOPaint: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        logger.info("🛑 IOPaint server stopped by user")
        sys.exit(0)

def main():
    """主函数 - 并行启动IOPaint和模糊服务"""
    logger.info("🚀 Starting Image Processing Services...")

    try:
        # 启动Flask模糊服务（后台线程）
        flask_thread = threading.Thread(target=start_flask_server, daemon=True)
        flask_thread.start()

        # 等待Flask服务启动
        time.sleep(2)
        logger.info("✅ Blur service started successfully")

        # 启动IOPaint服务（主线程）
        start_iopaint()

    except Exception as e:
        logger.error(f"❌ Error in main: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()