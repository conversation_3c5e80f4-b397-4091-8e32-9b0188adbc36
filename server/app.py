#!/usr/bin/env python3
"""
IOPaint server with integrated blur functionality
Uses IOPaint's built-in CLI with additional blur API endpoints
"""

import subprocess
import sys
import os
import threading
import time
from flask import Flask, request, jsonify
from flask_cors import CORS
import logging

# 导入模糊服务
from blur_service import blur_service

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建Flask应用
app = Flask(__name__)
CORS(app)  # 允许跨域请求

@app.route('/api/v1/blur', methods=['POST'])
def apply_blur():
    """应用模糊效果的API端点"""
    try:
        # 获取请求数据
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No JSON data provided'}), 400

        image_base64 = data.get('image')
        blur_type = data.get('type', 'gaussian')
        blur_radius = data.get('radius', 5)
        angle = data.get('angle', 0)

        if not image_base64:
            return jsonify({'error': 'No image data provided'}), 400

        # 确保参数类型正确
        try:
            blur_radius = int(blur_radius)
            angle = int(angle) if angle is not None else 0
        except (ValueError, TypeError):
            return jsonify({'error': 'Invalid parameter types'}), 400

        # 处理模糊请求
        result = blur_service.process_blur(image_base64, blur_type, blur_radius, angle)

        if result['success']:
            return jsonify(result)
        else:
            return jsonify({'error': result['error']}), 400

    except Exception as e:
        logger.error(f"Blur API error: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@app.route('/api/v1/blur/health', methods=['GET'])
def blur_health_check():
    """模糊服务健康检查端点"""
    return jsonify({
        'status': 'healthy',
        'service': 'blur-service',
        'max_blur_radius': blur_service.max_blur_radius,
        'max_image_size': blur_service.max_image_size
    })

def start_flask_server():
    """启动Flask服务器"""
    try:
        logger.info("🎨 Starting blur service on port 7861...")
        app.run(host='0.0.0.0', port=7861, debug=False, threaded=True)
    except Exception as e:
        logger.error(f"❌ Error starting Flask server: {e}")

def start_iopaint():
    """启动IOPaint服务"""
    # Set environment variables for better performance
    os.environ["PYTORCH_ENABLE_MPS_FALLBACK"] = "1"
    os.environ["ONEDNN_PRIMITIVE_CACHE_CAPACITY"] = "1024"  # 增加缓存
    os.environ["LRU_CACHE_CAPACITY"] = "1024"
    os.environ["TORCH_CUDNN_V8_API_LRU_CACHE_LIMIT"] = "1024"
    os.environ["OMP_NUM_THREADS"] = "8"  # 利用多核 CPU
    os.environ["MKL_NUM_THREADS"] = "8"
    os.environ["NUMEXPR_NUM_THREADS"] = "8"

    # 检测可用设备
    device = "cpu"
    try:
        import torch
        if torch.cuda.is_available():
            device = "cuda"
            logger.info("🚀 CUDA GPU detected, using GPU acceleration")
        else:
            logger.info("💻 Using CPU mode with optimizations")
    except ImportError:
        logger.info("⚠️  PyTorch not available, using CPU mode")

    # IOPaint CLI command with compatible optimizations
    cmd = [
        "iopaint", "start",
        "--model", "lama",  # 快速且高质量的模型
        "--host", "0.0.0.0",
        "--port", "7860",
        "--device", device,
        "--disable-nsfw-checker",
         "--enable-remove-bg"  # 启用背景移除功能
    ]

    # 根据设备类型添加优化参数
    if device == "cuda":
        # GPU 模式优化
        cmd.extend([
            "--cpu-offload",  # CPU 卸载以节省显存
            "--no-half"       # 避免半精度问题
        ])
    else:
        # CPU 模式优化
        cmd.extend([
            "--low-mem",      # 低内存模式
            "--cpu-textencoder"  # CPU 文本编码器
        ])

    logger.info("🎯 Starting IOPaint server...")
    logger.info(f"Command: {' '.join(cmd)}")

    try:
        # Run IOPaint CLI directly
        subprocess.run(cmd, check=True)
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ Error starting IOPaint: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        logger.info("🛑 IOPaint server stopped by user")
        sys.exit(0)

def main():
    """主函数 - 并行启动IOPaint和模糊服务"""
    logger.info("🚀 Starting Image Processing Services...")

    try:
        # 启动Flask模糊服务（后台线程）
        flask_thread = threading.Thread(target=start_flask_server, daemon=True)
        flask_thread.start()

        # 等待Flask服务启动
        time.sleep(2)
        logger.info("✅ Blur service started successfully")

        # 启动IOPaint服务（主线程）
        start_iopaint()

    except Exception as e:
        logger.error(f"❌ Error in main: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()