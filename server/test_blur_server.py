#!/usr/bin/env python3
"""
Test script for blur server
"""

import requests
import base64
import json
import sys
from PIL import Image
import io
import time

def create_test_image():
    """创建一个测试图片"""
    # 创建一个简单的测试图片
    img = Image.new('RGB', (200, 200), color='red')
    
    # 添加一些图案
    from PIL import ImageDraw
    draw = ImageDraw.Draw(img)
    draw.rectangle([50, 50, 150, 150], fill='blue')
    draw.ellipse([75, 75, 125, 125], fill='yellow')
    
    # 转换为base64
    buffer = io.BytesIO()
    img.save(buffer, format='PNG')
    return base64.b64encode(buffer.getvalue()).decode('utf-8')

def test_blur_server(base_url="http://localhost:7862"):
    """测试模糊服务器"""
    print("🧪 Testing Blur Server...")
    
    # 创建测试图片
    test_image_b64 = create_test_image()
    print("✅ Test image created")
    
    # 测试健康检查
    try:
        print("\n🔄 Testing health check...")
        response = requests.get(f"{base_url}/api/v1/blur/health", timeout=10)
        if response.status_code == 200:
            health_data = response.json()
            print(f"✅ Health check passed: {health_data}")
        else:
            print(f"❌ Health check failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False
    
    # 测试高斯模糊
    print("\n🔄 Testing Gaussian Blur...")
    try:
        payload = {
            "image": f"data:image/png;base64,{test_image_b64}",
            "type": "gaussian",
            "radius": 5
        }
        
        response = requests.post(
            f"{base_url}/api/v1/blur",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ Gaussian blur successful: {result['type']}, radius: {result['radius']}")
                
                # 保存结果图片（可选）
                if 'result' in result:
                    result_image_data = base64.b64decode(result['result'])
                    with open('test_gaussian_blur.png', 'wb') as f:
                        f.write(result_image_data)
                    print("💾 Result saved as test_gaussian_blur.png")
            else:
                print(f"❌ Gaussian blur failed: {result.get('error')}")
                return False
        else:
            print(f"❌ Gaussian blur request failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Gaussian blur error: {e}")
        return False
    
    # 测试运动模糊
    print("\n🔄 Testing Motion Blur...")
    try:
        payload = {
            "image": f"data:image/png;base64,{test_image_b64}",
            "type": "motion",
            "radius": 8,
            "angle": 45
        }
        
        response = requests.post(
            f"{base_url}/api/v1/blur",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ Motion blur successful: {result['type']}, radius: {result['radius']}, angle: {result['angle']}")
                
                # 保存结果图片（可选）
                if 'result' in result:
                    result_image_data = base64.b64decode(result['result'])
                    with open('test_motion_blur.png', 'wb') as f:
                        f.write(result_image_data)
                    print("💾 Result saved as test_motion_blur.png")
            else:
                print(f"❌ Motion blur failed: {result.get('error')}")
                return False
        else:
            print(f"❌ Motion blur request failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Motion blur error: {e}")
        return False
    
    print("\n🎉 All tests passed!")
    return True

def wait_for_server(base_url="http://localhost:7862", max_wait=30):
    """等待服务器启动"""
    print(f"⏳ Waiting for server at {base_url}...")
    
    for i in range(max_wait):
        try:
            response = requests.get(f"{base_url}/api/v1/blur/health", timeout=2)
            if response.status_code == 200:
                print(f"✅ Server is ready after {i+1} seconds")
                return True
        except:
            pass
        
        time.sleep(1)
        if i % 5 == 4:
            print(f"⏳ Still waiting... ({i+1}/{max_wait})")
    
    print(f"❌ Server not ready after {max_wait} seconds")
    return False

def main():
    """主函数"""
    if len(sys.argv) > 1:
        base_url = sys.argv[1]
    else:
        base_url = "http://localhost:7862"
    
    print(f"Testing blur server at: {base_url}")
    
    # 等待服务器启动
    if not wait_for_server(base_url):
        print("\n❌ Server is not available!")
        sys.exit(1)
    
    # 运行测试
    success = test_blur_server(base_url)
    
    if success:
        print("\n✅ Blur server is working correctly!")
        sys.exit(0)
    else:
        print("\n❌ Blur server tests failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
