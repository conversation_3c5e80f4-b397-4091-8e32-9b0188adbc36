#!/usr/bin/env python3
"""
Image Blur Service for IOPaint
Provides Gaussian and Motion blur effects
"""

import cv2
import numpy as np
from PIL import Image
import io
import base64
import logging
from typing import Tuple, Optional

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ImageBlurService:
    """图片模糊服务类"""
    
    def __init__(self):
        """初始化模糊服务"""
        self.max_image_size = 2048  # 最大图片尺寸限制
        self.max_blur_radius = 50   # 最大模糊半径限制
        logger.info("ImageBlurService initialized")
    
    def decode_base64_image(self, base64_str: str) -> np.ndarray:
        """
        解码base64图片为numpy数组
        
        Args:
            base64_str: base64编码的图片字符串
            
        Returns:
            numpy.ndarray: 图片数组 (RGB格式)
            
        Raises:
            ValueError: 当图片数据无效时
        """
        try:
            # 移除data:image/png;base64,前缀（如果存在）
            if ',' in base64_str:
                base64_str = base64_str.split(',')[1]
            
            # 解码base64
            image_data = base64.b64decode(base64_str)
            
            # 转换为PIL Image
            pil_image = Image.open(io.BytesIO(image_data))
            
            # 检查图片尺寸
            width, height = pil_image.size
            if width > self.max_image_size or height > self.max_image_size:
                # 等比例缩放到最大尺寸
                ratio = min(self.max_image_size / width, self.max_image_size / height)
                new_size = (int(width * ratio), int(height * ratio))
                pil_image = pil_image.resize(new_size, Image.Resampling.LANCZOS)
                logger.info(f"Image resized from {width}x{height} to {new_size[0]}x{new_size[1]}")
            
            # 转换为RGB（如果是RGBA或其他格式）
            if pil_image.mode != 'RGB':
                pil_image = pil_image.convert('RGB')
            
            # 转换为numpy数组
            return np.array(pil_image)
            
        except Exception as e:
            logger.error(f"Failed to decode base64 image: {e}")
            raise ValueError(f"Invalid image data: {e}")
    
    def encode_image_to_base64(self, image_array: np.ndarray) -> str:
        """
        将numpy数组编码为base64字符串
        
        Args:
            image_array: 图片数组
            
        Returns:
            str: base64编码的图片字符串
            
        Raises:
            ValueError: 当编码失败时
        """
        try:
            # 确保数据类型正确
            if image_array.dtype != np.uint8:
                image_array = np.clip(image_array, 0, 255).astype(np.uint8)
            
            # 转换为PIL Image
            pil_image = Image.fromarray(image_array)
            
            # 保存为bytes
            buffer = io.BytesIO()
            pil_image.save(buffer, format='PNG', optimize=True)
            
            # 编码为base64
            return base64.b64encode(buffer.getvalue()).decode('utf-8')
            
        except Exception as e:
            logger.error(f"Failed to encode image to base64: {e}")
            raise ValueError(f"Failed to encode image: {e}")
    
    def apply_gaussian_blur(self, image_array: np.ndarray, radius: int) -> np.ndarray:
        """
        应用高斯模糊
        
        Args:
            image_array: 输入图片数组
            radius: 模糊半径
            
        Returns:
            numpy.ndarray: 模糊后的图片数组
            
        Raises:
            ValueError: 当参数无效时
        """
        try:
            # 验证参数
            if radius < 1 or radius > self.max_blur_radius:
                raise ValueError(f"Blur radius must be between 1 and {self.max_blur_radius}")
            
            # 确保核大小是奇数
            kernel_size = radius * 2 + 1
            
            # 应用高斯模糊
            blurred = cv2.GaussianBlur(image_array, (kernel_size, kernel_size), 0)
            
            logger.info(f"Applied Gaussian blur with radius {radius}")
            return blurred
            
        except Exception as e:
            logger.error(f"Gaussian blur failed: {e}")
            raise ValueError(f"Gaussian blur failed: {e}")
    
    def apply_motion_blur(self, image_array: np.ndarray, size: int, angle: int) -> np.ndarray:
        """
        应用运动模糊
        
        Args:
            image_array: 输入图片数组
            size: 模糊核大小
            angle: 运动角度（度）
            
        Returns:
            numpy.ndarray: 模糊后的图片数组
            
        Raises:
            ValueError: 当参数无效时
        """
        try:
            # 验证参数
            if size < 3 or size > self.max_blur_radius * 2:
                raise ValueError(f"Motion blur size must be between 3 and {self.max_blur_radius * 2}")
            
            if angle < 0 or angle > 360:
                raise ValueError("Angle must be between 0 and 360 degrees")
            
            # 创建运动模糊核
            kernel = self._create_motion_blur_kernel(size, angle)
            
            # 应用滤镜
            blurred = cv2.filter2D(image_array, -1, kernel)
            
            logger.info(f"Applied motion blur with size {size} and angle {angle}")
            return blurred
            
        except Exception as e:
            logger.error(f"Motion blur failed: {e}")
            raise ValueError(f"Motion blur failed: {e}")
    
    def _create_motion_blur_kernel(self, size: int, angle: int) -> np.ndarray:
        """
        创建运动模糊核
        
        Args:
            size: 核大小
            angle: 运动角度
            
        Returns:
            numpy.ndarray: 运动模糊核
        """
        # 确保尺寸是奇数
        if size % 2 == 0:
            size += 1
        
        # 创建基础核（水平线）
        kernel = np.zeros((size, size), dtype=np.float32)
        kernel[int((size-1)/2), :] = np.ones(size, dtype=np.float32)
        kernel = kernel / size
        
        # 如果角度不为0，旋转核
        if angle != 0:
            center = (size // 2, size // 2)
            M = cv2.getRotationMatrix2D(center, angle, 1.0)
            kernel = cv2.warpAffine(kernel, M, (size, size))
        
        return kernel
    
    def validate_blur_params(self, blur_type: str, radius: int, angle: Optional[int] = None) -> Tuple[bool, str]:
        """
        验证模糊参数
        
        Args:
            blur_type: 模糊类型 ('gaussian' 或 'motion')
            radius: 模糊半径
            angle: 运动角度（可选）
            
        Returns:
            Tuple[bool, str]: (是否有效, 错误信息)
        """
        if blur_type not in ['gaussian', 'motion']:
            return False, f"Unsupported blur type: {blur_type}. Must be 'gaussian' or 'motion'"
        
        if not isinstance(radius, int) or radius < 1 or radius > self.max_blur_radius:
            return False, f"Blur radius must be an integer between 1 and {self.max_blur_radius}"
        
        if blur_type == 'motion':
            if angle is None:
                return False, "Motion blur requires an angle parameter"
            if not isinstance(angle, int) or angle < 0 or angle > 360:
                return False, "Angle must be an integer between 0 and 360 degrees"
        
        return True, ""
    
    def process_blur(self, base64_image: str, blur_type: str, radius: int, angle: Optional[int] = None) -> dict:
        """
        处理模糊请求的主要方法
        
        Args:
            base64_image: base64编码的图片
            blur_type: 模糊类型
            radius: 模糊半径
            angle: 运动角度（可选）
            
        Returns:
            dict: 处理结果
        """
        try:
            # 验证参数
            is_valid, error_msg = self.validate_blur_params(blur_type, radius, angle)
            if not is_valid:
                return {'success': False, 'error': error_msg}
            
            # 解码图片
            image_array = self.decode_base64_image(base64_image)
            
            # 应用模糊效果
            if blur_type == 'gaussian':
                result_array = self.apply_gaussian_blur(image_array, radius)
            else:  # motion
                motion_size = radius * 2 + 1
                result_array = self.apply_motion_blur(image_array, motion_size, angle)
            
            # 编码结果
            result_base64 = self.encode_image_to_base64(result_array)
            
            return {
                'success': True,
                'result': result_base64,
                'type': blur_type,
                'radius': radius,
                'angle': angle if blur_type == 'motion' else None,
                'original_size': image_array.shape[:2][::-1],  # (width, height)
                'result_size': result_array.shape[:2][::-1]
            }
            
        except Exception as e:
            logger.error(f"Blur processing failed: {e}")
            return {'success': False, 'error': str(e)}

# 创建全局服务实例
blur_service = ImageBlurService()
