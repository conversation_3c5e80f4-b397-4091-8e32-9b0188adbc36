# IOPaint 图片模糊功能接入指南

## 📋 调研总结

### 当前状态
- **IOPaint 版本**: 1.5.3 (最新)
- **现有插件**: RemoveBG, GFPGAN, RealESRGAN, RestoreFormer, Interactive Segmentation, Anime Segmentation
- **模糊功能**: 目前IOPaint **没有内置的图片模糊插件**

### 技术架构分析
- **服务端**: Python Flask + IOPaint CLI
- **前端**: React + TypeScript
- **部署**: Hugging Face Spaces (Docker)
- **插件系统**: 基于Python类的插件架构

## 🎯 实现方案

### 方案一：自定义IOPaint插件 (推荐)

#### 1. 创建模糊插件

```python
# server/blur_plugin.py
import cv2
import numpy as np
from PIL import Image
import io
import base64

class BlurPlugin:
    """图片模糊插件"""
    
    def __init__(self):
        self.name = "ImageBlur"
        self.description = "Apply blur effects to images"
    
    def gaussian_blur(self, image_data: bytes, blur_radius: int = 5) -> bytes:
        """高斯模糊"""
        # 将base64转换为PIL Image
        image = Image.open(io.BytesIO(image_data))
        
        # 转换为OpenCV格式
        cv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
        
        # 应用高斯模糊
        kernel_size = blur_radius * 2 + 1  # 确保是奇数
        blurred = cv2.GaussianBlur(cv_image, (kernel_size, kernel_size), 0)
        
        # 转换回PIL格式
        result_image = Image.fromarray(cv2.cvtColor(blurred, cv2.COLOR_BGR2RGB))
        
        # 转换为bytes
        output = io.BytesIO()
        result_image.save(output, format='PNG')
        return output.getvalue()
    
    def motion_blur(self, image_data: bytes, blur_size: int = 15, angle: int = 0) -> bytes:
        """运动模糊"""
        image = Image.open(io.BytesIO(image_data))
        cv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
        
        # 创建运动模糊核
        kernel = self._create_motion_blur_kernel(blur_size, angle)
        blurred = cv2.filter2D(cv_image, -1, kernel)
        
        result_image = Image.fromarray(cv2.cvtColor(blurred, cv2.COLOR_BGR2RGB))
        output = io.BytesIO()
        result_image.save(output, format='PNG')
        return output.getvalue()
    
    def _create_motion_blur_kernel(self, size: int, angle: int):
        """创建运动模糊核"""
        kernel = np.zeros((size, size))
        kernel[int((size-1)/2), :] = np.ones(size)
        kernel = kernel / size
        
        # 旋转核
        M = cv2.getRotationMatrix2D((size/2, size/2), angle, 1)
        kernel = cv2.warpAffine(kernel, M, (size, size))
        return kernel
```

#### 2. 修改服务端启动脚本

```python
# server/app.py (修改部分)
from blur_plugin import BlurPlugin
from flask import Flask, request, jsonify
import base64

app = Flask(__name__)
blur_plugin = BlurPlugin()

@app.route('/api/v1/blur', methods=['POST'])
def apply_blur():
    """应用模糊效果的API端点"""
    try:
        data = request.get_json()
        image_base64 = data.get('image')
        blur_type = data.get('type', 'gaussian')  # gaussian, motion
        blur_radius = data.get('radius', 5)
        angle = data.get('angle', 0)  # 仅用于运动模糊
        
        # 解码base64图片
        image_data = base64.b64decode(image_base64.split(',')[1] if ',' in image_base64 else image_base64)
        
        # 应用模糊效果
        if blur_type == 'gaussian':
            result_data = blur_plugin.gaussian_blur(image_data, blur_radius)
        elif blur_type == 'motion':
            result_data = blur_plugin.motion_blur(image_data, blur_radius, angle)
        else:
            return jsonify({'error': 'Unsupported blur type'}), 400
        
        # 返回base64编码的结果
        result_base64 = base64.b64encode(result_data).decode('utf-8')
        return jsonify({'result': result_base64})
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# 在main函数中添加Flask路由
def main():
    # ... 现有代码 ...
    
    # 启动Flask服务器（在IOPaint之前或并行）
    from threading import Thread
    flask_thread = Thread(target=lambda: app.run(host='0.0.0.0', port=7861))
    flask_thread.daemon = True
    flask_thread.start()
    
    # ... IOPaint启动代码 ...
```

#### 3. 更新依赖

```txt
# server/requirements.txt
IOPaint
rembg
onnxruntime
opencv-python
flask
```

### 方案二：前端实现 (简单快速)

#### 1. 创建模糊服务

```typescript
// lib/blur-service.ts
export interface BlurOptions {
  type: 'gaussian' | 'motion';
  radius: number;
  angle?: number; // 仅用于运动模糊
}

export const applyBlur = async (
  imageBase64: string,
  options: BlurOptions
): Promise<string> => {
  try {
    const response = await fetch(`${BASE_URL}/api/v1/blur`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        image: imageBase64,
        type: options.type,
        radius: options.radius,
        angle: options.angle || 0,
      }),
    });

    if (!response.ok) {
      throw new Error(`Blur API error: ${response.statusText}`);
    }

    const result = await response.json();
    return `data:image/png;base64,${result.result}`;
  } catch (error) {
    console.error('Blur error:', error);
    throw error;
  }
};

// Canvas实现的前端模糊（备选方案）
export const applyCanvasBlur = (
  imageUrl: string,
  blurRadius: number
): Promise<string> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = 'anonymous';
    
    img.onload = () => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      if (!ctx) {
        reject(new Error('Failed to get canvas context'));
        return;
      }
      
      canvas.width = img.width;
      canvas.height = img.height;
      
      // 应用CSS滤镜模糊
      ctx.filter = `blur(${blurRadius}px)`;
      ctx.drawImage(img, 0, 0);
      
      resolve(canvas.toDataURL('image/png'));
    };
    
    img.onerror = reject;
    img.src = imageUrl;
  });
};
```

#### 2. 添加模糊按钮组件

```tsx
// components/BlurControls.tsx
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { Blur, RotateCw } from 'lucide-react';

interface BlurControlsProps {
  onApplyBlur: (type: 'gaussian' | 'motion', radius: number, angle?: number) => void;
  disabled?: boolean;
  isProcessing?: boolean;
}

export const BlurControls: React.FC<BlurControlsProps> = ({
  onApplyBlur,
  disabled = false,
  isProcessing = false,
}) => {
  const [blurRadius, setBlurRadius] = useState(5);
  const [motionAngle, setMotionAngle] = useState(0);
  const [blurType, setBlurType] = useState<'gaussian' | 'motion'>('gaussian');

  return (
    <div className="space-y-4 p-4 bg-white border border-gray-200 rounded-lg">
      <h4 className="text-sm font-medium text-gray-700 flex items-center gap-2">
        <Blur className="w-4 h-4 text-indigo-500" />
        Blur Effects
      </h4>

      {/* 模糊类型选择 */}
      <div className="flex gap-2">
        <Button
          variant={blurType === 'gaussian' ? 'default' : 'outline'}
          size="sm"
          onClick={() => setBlurType('gaussian')}
          disabled={disabled || isProcessing}
        >
          Gaussian
        </Button>
        <Button
          variant={blurType === 'motion' ? 'default' : 'outline'}
          size="sm"
          onClick={() => setBlurType('motion')}
          disabled={disabled || isProcessing}
        >
          Motion
        </Button>
      </div>

      {/* 模糊半径 */}
      <div className="space-y-2">
        <div className="flex justify-between">
          <label className="text-xs text-gray-600">Blur Radius</label>
          <span className="text-xs text-gray-500">{blurRadius}px</span>
        </div>
        <Slider
          value={[blurRadius]}
          onValueChange={([value]) => setBlurRadius(value)}
          min={1}
          max={20}
          step={1}
          disabled={disabled || isProcessing}
        />
      </div>

      {/* 运动模糊角度 */}
      {blurType === 'motion' && (
        <div className="space-y-2">
          <div className="flex justify-between">
            <label className="text-xs text-gray-600 flex items-center gap-1">
              <RotateCw className="w-3 h-3" />
              Angle
            </label>
            <span className="text-xs text-gray-500">{motionAngle}°</span>
          </div>
          <Slider
            value={[motionAngle]}
            onValueChange={([value]) => setMotionAngle(value)}
            min={0}
            max={360}
            step={15}
            disabled={disabled || isProcessing}
          />
        </div>
      )}

      {/* 应用按钮 */}
      <Button
        onClick={() => onApplyBlur(blurType, blurRadius, motionAngle)}
        disabled={disabled || isProcessing}
        className="w-full bg-indigo-600 hover:bg-indigo-700 text-white"
      >
        <Blur className="w-4 h-4 mr-2" />
        {isProcessing ? 'Applying Blur...' : 'Apply Blur'}
      </Button>
    </div>
  );
};
```

## 📝 集成步骤

### 服务端修改

1. **添加模糊插件代码**到 `server/blur_plugin.py`
2. **修改 `server/app.py`** 添加Flask路由
3. **更新 `server/requirements.txt`** 添加opencv-python和flask
4. **重新部署**到Hugging Face Spaces

### 前端修改

1. **创建模糊服务** `lib/blur-service.ts`
2. **添加模糊控件** `components/BlurControls.tsx`
3. **集成到ToolPanel** 添加模糊控制面板
4. **更新ImageEditor** 添加模糊处理逻辑
5. **添加状态管理** 处理模糊结果和历史记录

## 🚀 部署注意事项

### Hugging Face Spaces限制
- **内存限制**: 16GB RAM
- **存储限制**: 50GB
- **计算限制**: CPU优先，GPU有限
- **网络限制**: 出站请求受限

### 性能优化建议
- 使用较小的模糊半径以减少计算量
- 实现图片尺寸限制（如最大1920x1080）
- 添加缓存机制避免重复计算
- 考虑使用WebAssembly进行前端处理

## 📊 预期效果

用户将能够：
- 选择高斯模糊或运动模糊
- 调整模糊强度（半径）
- 设置运动模糊角度
- 预览模糊效果
- 下载模糊后的图片
- 与其他功能（背景移除、对象移除）组合使用

这个方案提供了完整的图片模糊功能，与现有的IOPaint架构完美集成。

## 🔧 具体实现代码

### 前端集成示例

#### 1. 更新ImageEditor组件

```tsx
// components/ImageEditor.tsx (添加部分)
import { applyBlur, type BlurOptions } from '@/lib/blur-service';

// 在ImageEditor组件中添加状态
const [blurProcessingStates, setBlurProcessingStates] = useState<Record<string, boolean>>({});
const [blurResults, setBlurResults] = useState<Record<string, string>>({});

// 添加模糊处理函数
const handleApplyBlur = useCallback(async (
  type: 'gaussian' | 'motion',
  radius: number,
  angle?: number
) => {
  const currentImage = getCurrentImage();
  if (!currentImage) return;

  try {
    // 设置处理状态
    setBlurProcessingStates(prev => ({ ...prev, [currentImage.id]: true }));
    setError(null);

    // 获取当前图片（优先使用处理后的结果）
    const finalUrl = getCurrentFinalResult()?.url;
    const currentProcessedUrl = getCurrentProcessedUrl();
    const currentBackgroundRemovedUrl = getCurrentBackgroundRemovedUrl();
    const imageUrl = finalUrl || currentProcessedUrl || currentBackgroundRemovedUrl || currentImage.url;

    // 转换为base64
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (!ctx) throw new Error('Failed to get canvas context');

    const img = new Image();
    img.crossOrigin = 'anonymous';

    await new Promise((resolve, reject) => {
      img.onload = resolve;
      img.onerror = reject;
      img.src = imageUrl;
    });

    canvas.width = img.naturalWidth;
    canvas.height = img.naturalHeight;
    ctx.drawImage(img, 0, 0);

    const imageBase64 = canvas.toDataURL('image/png');

    // 调用模糊API
    const blurOptions: BlurOptions = { type, radius, angle };
    const resultUrl = await applyBlur(imageBase64, blurOptions);

    // 保存结果
    setBlurResults(prev => ({ ...prev, [currentImage.id]: resultUrl }));

  } catch (err) {
    setError(err instanceof Error ? err.message : 'Failed to apply blur effect');
    console.error('Blur error:', err);
  } finally {
    setBlurProcessingStates(prev => ({ ...prev, [currentImage.id]: false }));
  }
}, [getCurrentImage, getCurrentFinalResult, getCurrentProcessedUrl, getCurrentBackgroundRemovedUrl]);
```

#### 2. 更新ToolPanel组件

```tsx
// components/ToolPanel.tsx (添加部分)
import { BlurControls } from './BlurControls';

// 在ToolPanel的render中添加
<Card className="p-4 bg-white border border-gray-200 shadow-sm">
  <BlurControls
    onApplyBlur={onApplyBlur}
    disabled={disabled}
    isProcessing={isBlurProcessing}
  />
</Card>
```

### 服务端完整实现

#### 1. 创建独立的模糊服务

```python
# server/blur_service.py
from flask import Flask, request, jsonify, send_file
from flask_cors import CORS
import cv2
import numpy as np
from PIL import Image
import io
import base64
import logging

app = Flask(__name__)
CORS(app)  # 允许跨域请求

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ImageBlurService:
    """图片模糊服务"""

    @staticmethod
    def decode_base64_image(base64_str: str) -> np.ndarray:
        """解码base64图片为numpy数组"""
        try:
            # 移除data:image/png;base64,前缀
            if ',' in base64_str:
                base64_str = base64_str.split(',')[1]

            # 解码base64
            image_data = base64.b64decode(base64_str)

            # 转换为PIL Image
            pil_image = Image.open(io.BytesIO(image_data))

            # 转换为RGB（如果是RGBA）
            if pil_image.mode == 'RGBA':
                pil_image = pil_image.convert('RGB')

            # 转换为numpy数组
            return np.array(pil_image)

        except Exception as e:
            logger.error(f"Failed to decode base64 image: {e}")
            raise ValueError(f"Invalid image data: {e}")

    @staticmethod
    def encode_image_to_base64(image_array: np.ndarray) -> str:
        """将numpy数组编码为base64"""
        try:
            # 转换为PIL Image
            pil_image = Image.fromarray(image_array.astype(np.uint8))

            # 保存为bytes
            buffer = io.BytesIO()
            pil_image.save(buffer, format='PNG')

            # 编码为base64
            return base64.b64encode(buffer.getvalue()).decode('utf-8')

        except Exception as e:
            logger.error(f"Failed to encode image to base64: {e}")
            raise ValueError(f"Failed to encode image: {e}")

    def apply_gaussian_blur(self, image_array: np.ndarray, radius: int) -> np.ndarray:
        """应用高斯模糊"""
        try:
            # 确保半径是奇数
            kernel_size = radius * 2 + 1

            # 应用高斯模糊
            blurred = cv2.GaussianBlur(image_array, (kernel_size, kernel_size), 0)

            return blurred

        except Exception as e:
            logger.error(f"Gaussian blur failed: {e}")
            raise ValueError(f"Gaussian blur failed: {e}")

    def apply_motion_blur(self, image_array: np.ndarray, size: int, angle: int) -> np.ndarray:
        """应用运动模糊"""
        try:
            # 创建运动模糊核
            kernel = self._create_motion_blur_kernel(size, angle)

            # 应用滤镜
            blurred = cv2.filter2D(image_array, -1, kernel)

            return blurred

        except Exception as e:
            logger.error(f"Motion blur failed: {e}")
            raise ValueError(f"Motion blur failed: {e}")

    def _create_motion_blur_kernel(self, size: int, angle: int) -> np.ndarray:
        """创建运动模糊核"""
        # 确保尺寸是奇数
        if size % 2 == 0:
            size += 1

        # 创建核
        kernel = np.zeros((size, size))
        kernel[int((size-1)/2), :] = np.ones(size)
        kernel = kernel / size

        # 旋转核
        center = (size // 2, size // 2)
        M = cv2.getRotationMatrix2D(center, angle, 1)
        kernel = cv2.warpAffine(kernel, M, (size, size))

        return kernel

# 创建服务实例
blur_service = ImageBlurService()

@app.route('/api/v1/blur', methods=['POST'])
def apply_blur():
    """应用模糊效果的API端点"""
    try:
        # 获取请求数据
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No JSON data provided'}), 400

        image_base64 = data.get('image')
        blur_type = data.get('type', 'gaussian')
        blur_radius = int(data.get('radius', 5))
        angle = int(data.get('angle', 0))

        if not image_base64:
            return jsonify({'error': 'No image data provided'}), 400

        # 验证参数
        if blur_radius < 1 or blur_radius > 50:
            return jsonify({'error': 'Blur radius must be between 1 and 50'}), 400

        if angle < 0 or angle > 360:
            return jsonify({'error': 'Angle must be between 0 and 360'}), 400

        # 解码图片
        image_array = blur_service.decode_base64_image(image_base64)

        # 应用模糊效果
        if blur_type == 'gaussian':
            result_array = blur_service.apply_gaussian_blur(image_array, blur_radius)
        elif blur_type == 'motion':
            result_array = blur_service.apply_motion_blur(image_array, blur_radius * 2 + 1, angle)
        else:
            return jsonify({'error': f'Unsupported blur type: {blur_type}'}), 400

        # 编码结果
        result_base64 = blur_service.encode_image_to_base64(result_array)

        return jsonify({
            'success': True,
            'result': result_base64,
            'type': blur_type,
            'radius': blur_radius,
            'angle': angle if blur_type == 'motion' else None
        })

    except ValueError as e:
        logger.error(f"Validation error: {e}")
        return jsonify({'error': str(e)}), 400
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查端点"""
    return jsonify({'status': 'healthy', 'service': 'blur-service'})

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=7861, debug=False)
```

#### 2. 修改主启动脚本

```python
# server/app.py (修改后的完整版本)
#!/usr/bin/env python3
"""
IOPaint server with blur functionality
"""

import subprocess
import sys
import os
from threading import Thread
import time

def start_blur_service():
    """启动模糊服务"""
    try:
        print("🎨 Starting blur service on port 7861...")
        subprocess.run([
            sys.executable, "blur_service.py"
        ], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Error starting blur service: {e}")
    except KeyboardInterrupt:
        print("🛑 Blur service stopped by user")

def start_iopaint():
    """启动IOPaint服务"""
    # 设置环境变量
    os.environ["PYTORCH_ENABLE_MPS_FALLBACK"] = "1"
    os.environ["ONEDNN_PRIMITIVE_CACHE_CAPACITY"] = "1024"
    os.environ["LRU_CACHE_CAPACITY"] = "1024"
    os.environ["TORCH_CUDNN_V8_API_LRU_CACHE_LIMIT"] = "1024"
    os.environ["OMP_NUM_THREADS"] = "8"
    os.environ["MKL_NUM_THREADS"] = "8"
    os.environ["NUMEXPR_NUM_THREADS"] = "8"

    # 检测设备
    device = "cpu"
    try:
        import torch
        if torch.cuda.is_available():
            device = "cuda"
            print("🚀 CUDA GPU detected, using GPU acceleration")
        else:
            print("💻 Using CPU mode with optimizations")
    except ImportError:
        print("⚠️  PyTorch not available, using CPU mode")

    # IOPaint命令
    cmd = [
        "iopaint", "start",
        "--model", "lama",
        "--host", "0.0.0.0",
        "--port", "7860",
        "--device", device,
        "--disable-nsfw-checker",
        "--enable-remove-bg"
    ]

    # 设备优化参数
    if device == "cuda":
        cmd.extend(["--cpu-offload", "--no-half"])
    else:
        cmd.extend(["--low-mem", "--cpu-textencoder"])

    print("🎯 Starting IOPaint server...")
    print(f"Command: {' '.join(cmd)}")

    try:
        subprocess.run(cmd, check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Error starting IOPaint: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        print("🛑 IOPaint server stopped by user")
        sys.exit(0)

def main():
    """主函数 - 并行启动两个服务"""
    print("🚀 Starting Image Processing Services...")

    # 启动模糊服务（后台线程）
    blur_thread = Thread(target=start_blur_service, daemon=True)
    blur_thread.start()

    # 等待模糊服务启动
    time.sleep(2)

    # 启动IOPaint服务（主线程）
    start_iopaint()

if __name__ == "__main__":
    main()
```

## 🎯 最终效果

实现后，用户将在ToolPanel中看到新的"Blur Effects"面板，可以：

1. **选择模糊类型**：高斯模糊或运动模糊
2. **调整模糊强度**：1-20px半径
3. **设置运动角度**：0-360度（运动模糊）
4. **实时预览**：在Result Preview中查看效果
5. **组合使用**：与背景移除、对象移除等功能结合
6. **下载结果**：保存模糊后的图片

这个完整的解决方案为您的IOPaint项目添加了专业级的图片模糊功能！🎨
