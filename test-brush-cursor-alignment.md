# 画笔尺寸与光标对齐优化测试

## 问题描述
在调整Brush Size之后，在画布中涂抹时，涂抹出来的尺寸没有与当前鼠标的cursor的尺寸重合，体验不好。

## 优化方案

### 1. 添加缩放支持
- 在 `MaskCanvas` 组件中添加了 `zoom` 属性
- 在 `CanvasEditor` 中将当前缩放级别传递给 `MaskCanvas`

### 2. 改进光标尺寸计算
- 创建了 `getCursorSize()` 函数，考虑：
  - 画笔设置的尺寸
  - 当前缩放级别
  - 画布实际分辨率与显示尺寸的比例
  - 最小和最大光标尺寸限制

### 3. 改进画笔绘制尺寸计算
- 添加了 `getActualBrushSize()` 函数，计算考虑画布缩放的实际画笔尺寸
- 更新了 `drawLine` 函数使用实际画笔尺寸
- 更新了绘制函数中的所有画笔尺寸计算

### 4. 优化光标视觉效果
- 改进了 `createDynamicCircleSVG` 函数：
  - 添加了动态边框宽度
  - 改进了阴影效果
  - 添加了多层圆形显示（外圈、内圈、中心点）
  - 更好的透明度处理

## 测试步骤

1. **基本功能测试**：
   - 上传一张图片
   - 调整画笔尺寸从最小(5px)到最大(100px)
   - 验证光标尺寸是否与设置的画笔尺寸匹配

2. **缩放测试**：
   - 使用缩放控件放大画布(1.5x, 2x, 3x)
   - 验证光标尺寸是否随缩放正确调整
   - 验证绘制的笔触尺寸是否与光标匹配

3. **缩小测试**：
   - 缩小画布(0.5x, 0.3x, 0.1x)
   - 验证光标在小尺寸下仍然可见
   - 验证绘制精度

4. **不同画笔尺寸测试**：
   - 测试小画笔(5-20px)
   - 测试中等画笔(20-50px)  
   - 测试大画笔(50-100px)
   - 验证每种尺寸下光标与实际绘制的匹配度

## 预期结果

- 光标尺寸应该准确反映实际绘制的画笔尺寸
- 在不同缩放级别下，光标与绘制结果保持一致
- 光标在所有尺寸下都保持可见性和可用性
- 绘制体验更加直观和准确

## 技术细节

### 关键函数
1. `getCursorSize()` - 计算光标显示尺寸
2. `getActualBrushSize()` - 计算实际画笔绘制尺寸
3. `createDynamicCircleSVG()` - 创建动态光标SVG

### 计算公式
```javascript
// 实际画笔尺寸 = 设置尺寸 × 画布分辨率比例
actualBrushSize = brushSettings.size * (canvas.width / rect.width)

// 光标尺寸 = 实际画笔尺寸 × 显示比例 × 缩放级别
cursorSize = actualBrushSize * (rect.width / canvas.width) * zoom
```

这样确保了光标尺寸与实际绘制尺寸的完美匹配。
