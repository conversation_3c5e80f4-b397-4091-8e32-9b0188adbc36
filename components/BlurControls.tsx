"use client";

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { Card } from '@/components/ui/card';
import { Blur, RotateCw, Zap, Wind } from 'lucide-react';
import { BlurOptions, BLUR_PRESETS } from '@/lib/blur-service';

interface BlurControlsProps {
  onApplyBlur: (options: BlurOptions) => void;
  disabled?: boolean;
  isProcessing?: boolean;
}

export const BlurControls: React.FC<BlurControlsProps> = ({
  onApplyBlur,
  disabled = false,
  isProcessing = false,
}) => {
  const [blurType, setBlurType] = useState<'gaussian' | 'motion'>('gaussian');
  const [blurRadius, setBlurRadius] = useState(5);
  const [motionAngle, setMotionAngle] = useState(0);
  const [showAdvanced, setShowAdvanced] = useState(false);

  const handleApplyBlur = () => {
    const options: BlurOptions = {
      type: blurType,
      radius: blurRadius,
      ...(blurType === 'motion' && { angle: motionAngle })
    };
    onApplyBlur(options);
  };

  const handlePresetBlur = (presetName: keyof typeof BLUR_PRESETS) => {
    const preset = BLUR_PRESETS[presetName];
    setBlurType(preset.type);
    setBlurRadius(preset.radius);
    if (preset.angle !== undefined) {
      setMotionAngle(preset.angle);
    }
    onApplyBlur(preset);
  };

  return (
    <Card className="p-4 bg-white border border-gray-200 shadow-sm">
      <h4 className="text-sm font-medium text-gray-700 mb-4 flex items-center gap-2">
        <Blur className="w-4 h-4 text-orange-500" />
        Blur Effects
      </h4>

      {/* Quick Presets */}
      <div className="space-y-3 mb-4">
        <div className="text-xs text-gray-600 font-medium">Quick Presets</div>
        <div className="grid grid-cols-2 gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePresetBlur('light_gaussian')}
            disabled={disabled || isProcessing}
            className="text-xs h-8 border-orange-200 text-orange-700 hover:bg-orange-50"
          >
            <Blur className="w-3 h-3 mr-1" />
            Light
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePresetBlur('medium_gaussian')}
            disabled={disabled || isProcessing}
            className="text-xs h-8 border-orange-200 text-orange-700 hover:bg-orange-50"
          >
            <Blur className="w-3 h-3 mr-1" />
            Medium
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePresetBlur('heavy_gaussian')}
            disabled={disabled || isProcessing}
            className="text-xs h-8 border-orange-200 text-orange-700 hover:bg-orange-50"
          >
            <Blur className="w-3 h-3 mr-1" />
            Heavy
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePresetBlur('horizontal_motion')}
            disabled={disabled || isProcessing}
            className="text-xs h-8 border-orange-200 text-orange-700 hover:bg-orange-50"
          >
            <Wind className="w-3 h-3 mr-1" />
            Motion
          </Button>
        </div>
      </div>

      {/* Advanced Controls Toggle */}
      <div className="mb-3">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setShowAdvanced(!showAdvanced)}
          disabled={disabled || isProcessing}
          className="text-xs text-gray-600 hover:text-gray-800 p-0 h-auto"
        >
          <Zap className="w-3 h-3 mr-1" />
          {showAdvanced ? 'Hide' : 'Show'} Advanced Controls
        </Button>
      </div>

      {/* Advanced Controls */}
      {showAdvanced && (
        <div className="space-y-4 border-t border-gray-100 pt-4">
          {/* Blur Type Selection */}
          <div className="space-y-2">
            <div className="text-xs text-gray-600 font-medium">Blur Type</div>
            <div className="flex gap-2">
              <Button
                variant={blurType === 'gaussian' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setBlurType('gaussian')}
                disabled={disabled || isProcessing}
                className={`text-xs h-8 flex-1 ${
                  blurType === 'gaussian'
                    ? 'bg-orange-600 hover:bg-orange-700 text-white'
                    : 'border-orange-200 text-orange-700 hover:bg-orange-50'
                }`}
              >
                <Blur className="w-3 h-3 mr-1" />
                Gaussian
              </Button>
              <Button
                variant={blurType === 'motion' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setBlurType('motion')}
                disabled={disabled || isProcessing}
                className={`text-xs h-8 flex-1 ${
                  blurType === 'motion'
                    ? 'bg-orange-600 hover:bg-orange-700 text-white'
                    : 'border-orange-200 text-orange-700 hover:bg-orange-50'
                }`}
              >
                <Wind className="w-3 h-3 mr-1" />
                Motion
              </Button>
            </div>
          </div>

          {/* Blur Radius */}
          <div className="space-y-2">
            <div className="flex justify-between">
              <label className="text-xs text-gray-600 font-medium">Blur Radius</label>
              <span className="text-xs text-gray-500">{blurRadius}px</span>
            </div>
            <Slider
              value={[blurRadius]}
              onValueChange={([value]) => setBlurRadius(value)}
              min={1}
              max={25}
              step={1}
              disabled={disabled || isProcessing}
              className="w-full"
            />
          </div>

          {/* Motion Blur Angle */}
          {blurType === 'motion' && (
            <div className="space-y-2">
              <div className="flex justify-between">
                <label className="text-xs text-gray-600 font-medium flex items-center gap-1">
                  <RotateCw className="w-3 h-3" />
                  Motion Angle
                </label>
                <span className="text-xs text-gray-500">{motionAngle}°</span>
              </div>
              <Slider
                value={[motionAngle]}
                onValueChange={([value]) => setMotionAngle(value)}
                min={0}
                max={360}
                step={15}
                disabled={disabled || isProcessing}
                className="w-full"
              />
              <div className="flex justify-between text-xs text-gray-400">
                <span>0° (→)</span>
                <span>90° (↓)</span>
                <span>180° (←)</span>
                <span>270° (↑)</span>
              </div>
            </div>
          )}

          {/* Apply Button */}
          <Button
            onClick={handleApplyBlur}
            disabled={disabled || isProcessing}
            className="w-full bg-orange-600 hover:bg-orange-700 text-white shadow-sm"
          >
            <Blur className="w-4 h-4 mr-2" />
            {isProcessing ? 'Applying Blur...' : 'Apply Custom Blur'}
          </Button>
        </div>
      )}

      {/* Processing Indicator */}
      {isProcessing && (
        <div className="mt-3 p-2 bg-orange-50 border border-orange-200 rounded-md">
          <div className="flex items-center gap-2 text-xs text-orange-700">
            <div className="w-3 h-3 border-2 border-orange-600 border-t-transparent rounded-full animate-spin"></div>
            Processing blur effect...
          </div>
        </div>
      )}
    </Card>
  );
};
